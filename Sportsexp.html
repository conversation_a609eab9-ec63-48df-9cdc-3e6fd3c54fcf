<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Page Title -->
    <title><PERSON> - Online Portfolio</title>

    <!-- Meta Tags -->
    <meta name="description" content="Porfolio online">
    <meta name="keywords"
        content=" resume, portfolio, personal page, cv, template, one page, responsive, html5, css3, creative, clean">
    <meta name="author" content="Lucas">

    <!-- Viewport Meta-->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

    <!-- Template Favicon & Icons Start -->
    <link rel="icon" href="img/favicon/Asuka.png" sizes="any">
    <link rel="icon" href="img/favicon/Asuka.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="img/favicon/apple-touch-icon.png">
    <link rel="manifest" href="img/favicon/manifest.webmanifest">
    <!-- Template Favicon & Icons End -->

    <!-- Facebook Metadata Start -->
    <meta property="og:image:height" content="1200">
    <meta property="og:image:width" content="1200">
    <meta property="og:title" content="Lucas Schoenherr - Online Portfolio">
    <meta property="og:description" content="Lucas Schoenherr - Online Portfolio">
    <!-- Facebook Metadata End -->

    <!-- Template Styles Start -->
    <link rel="stylesheet" type="text/css" href="css/loaders/loader.css">
    <link rel="stylesheet" type="text/css" href="css/plugins.css">
    <link rel="stylesheet" type="text/css" href="css/main.css">

    <!-- Template Styles End -->

    <!-- Custom Browser Color Start -->
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#BABEC8">
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#141414">
    <meta name="msapplication-navbutton-color" content="#141414">
    <meta name="mobile-web-app-capable" content="yes">
    <!-- Custom Browser Color End -->

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/magnific-popup.js/1.1.0/magnific-popup.min.css">
</head>

<body data-bs-spy="scroll" data-bs-target=".sidebar-nav" data-bs-offset="200">

    <!-- Loader Start -->
    <div id="loader" class="loader">
        <div class="loader__wrapper">
            <div class="loader__content">
                <div class="loader__count">
                    <span class="count__text">0</span>
                    <span class="count__percent">%</span>
                </div>
            </div>
        </div>
    </div>
    <!-- Loader End -->

    <!-- Header Start -->
    <header id="header" class="header d-flex justify-content-center loading__fade loading-wrap">
        <!-- Navigation Menu Start -->
        <div class="header__navigation d-flex justify-content-start">
            <nav id="menu" class="menu">
                <ul class="menu__list d-flex justify-content-start">
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html">
                            <span class="menu__caption">Home</span>
                            <i class="ph ph-house-simple"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html#portfolio">
                            <span class="menu__caption">Portfólio</span>
                            <i class="ph ph-squares-four"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html#about">
                            <span class="menu__caption">Sobre</span>
                            <i class="ph ph-user"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html#services">
                            <span class="menu__caption">Especialidades</span>
                            <i class="ph ph-sticker"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html#resume">
                            <span class="menu__caption">Curriculum</span>
                            <i class="ph ph-article"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html#contact">
                            <span class="menu__caption">Contato</span>
                            <i class="ph ph-envelope"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
        <!-- Navigation Menu End -->
    </header>
    <!-- Header End -->

    <!-- Fixed Logo Start -->
    <div class="logo loading__fade">
        <a href="index.html" class="logo__link">
            <!-- logo icon -->
            <img src="img/favicon/Asuka.svg" alt="Logo Icon" style="width: 35px; height: 35px;">
            <!-- logo text -->
            <span class="logo-text">Lucas Schoenherr</span>
        </a>
    </div>
    <!-- Fixed Logo End -->

   <!-- Fixed Color Switch Start -->
   <div class="color loading__fade">
    <button id="color-switcher" class="color-switcher" type="button" role="switch" aria-label="light/dark mode" aria-checked="true"></button>
  </div>
  <!-- Fixed Color Switch End -->

    <!-- Page Content Start -->
    <main id="page-content" class="page-content">
        <section id="project" class="inner">
            <div class="inner__wrapper">
                <div class="container-fluid p-0">
                    <div class="row g-0">
                        <!-- Project Content Start -->
                        <div class="col-12 col-xl-8 offset-xl-2">
                            <div class="inner__content">
                                <!-- Project Header -->
                                <div class="main-wrapper">
                                    <div class="project-header">
                                        <h1 class="project-title">Sports Experience - World Cup 2022</h1>

                                        <div class="project-tags">
                                            <span class="tag direcTV">DirecTV</span>
                                            <span class="tag">Multiplatform</span>
                                            <span class="tag">Statistics</span>
                                            <span class="tag">Design System</span>
                                            <span class="tag">Real-time Data</span>
                                            <span class="tag">UX/UI Design</span>
                                            <span class="tag">Product Design</span>
                                        </div>

                                        <div class="project-description">
                                            O Sports Experience é um hub multiplataforma desenvolvido para a DirecTV durante a Copa do Mundo 2022 no Qatar. O projeto oferece transmissão de jogos ao vivo, estatísticas em tempo real, e acompanhamento completo do torneio através de mobile, web e TV. Com versões específicas para Brasil e América Latina, o sistema integra dados oficiais da FIFA para proporcionar uma experiência imersiva e completa e em tempo real aos usuários da DirecTV GO.
                                        </div>
                                        <div class="content__block loading__item disclaimer">
                                            <div class="disclaimer__content">
                                                <i class="ph ph-info"></i>
                                                <p class="disclaimer__text type-basic-160lh">
                                                    Todos os dados e métricas apresentados nas imagens são fictícios, gerados apenas para fins de demonstração. Os mesmos não refletem informações reais da empresa ou de suas operações.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="custom-swiper swiper sports-swiper">
                                    <div class="swiper-button-prev">
                                        <i class="ph ph-arrow-left"></i>
                                    </div>
                                    <div class="swiper-button-next">
                                        <i class="ph ph-arrow-right"></i>
                                    </div>
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <img src="/img/works/Sports/7.png" alt="Slide 1">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/Sports/6.png" alt="Slide 2">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/Sports/5.png" alt="Slide 3">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/Sports/4.png" alt="Slide 4">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/Sports/3.png" alt="Slide 5">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/Sports/2.png" alt="Slide 6">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/Sports/1.png" alt="Slide 7">
                                        </div>
                                    </div>
                                </div>

                                <!-- Disclaimer Section -->
                                

                                <!-- Project Data Section Start -->
                                <div class="content__block loading__item">
                                    <div class="project__data">
                                        <div class="container-fluid p-0">
                                            <div class="row g-0">
                                                <!-- project data single item -->
                                                <div class="col-12 col-md-3 grid-item pdata__item">
                                                    <p class="data__title tagline-chapter small type-basic-160lh">
                                                        Tipo</p>
                                                    <p class="data__descr small type-basic-160lh">
                                                        Mobile, Web e TV</p>
                                                </div>
                                                <!-- project data single item -->
                                                <div class="col-12 col-md-3 grid-item pdata__item">
                                                    <p class="data__title tagline-chapter small type-basic-160lh">
                                                        Ano</p>
                                                    <p class="data__descr small type-basic-160lh">
                                                        2022</p>
                                                </div>
                                                <!-- project data single item -->
                                                <div class="col-12 col-md-3 grid-item pdata__item">
                                                    <p class="data__title tagline-chapter small type-basic-160lh">
                                                        Papel</p>
                                                    <p class="data__descr small type-basic-160lh">
                                                        Senior Product Designer</p>
                                                </div>
                                                <!-- project data single item -->
                                                <div class="col-12 col-md-3 grid-item pdata__item">
                                                    <p class="data__title tagline-chapter small type-basic-160lh">
                                                        Cliente</p>
                                                    <p class="data__descr small type-basic-160lh">
                                                        DirecTV GO</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Project Data Section End -->

                                <!-- New Two-Column Layout Start -->
                                <div class="container-fluid p-0">
                                    <div class="row g-0">
                                        <div class="col-12 col-lg-3">
                                            <nav class="sidebar-nav loading__item" role="navigation" aria-label="Menu lateral">
                                                <ul class="nav flex-column list-unstyled">
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#overview"
                                                            aria-current="page">Overview</a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#desafio"
                                                            aria-current="page">Desafio</a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#papel">Papel</a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#solucao">Solução</a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#impacto">Entrega</a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#galeria">Galeria</a>
                                                    </li>
                                                </ul>
                                            </nav>
                                        </div>

                                        <!-- Main Content -->
                                        <div class="col-12 col-lg-9">
                                            <div class="main-content">
                                                <!-- Overview Section -->
                                                <div id="overview" class="project__block loading__item">
                                                    <h3>Overview do Projeto</h3>
                                                    <p class="type-basic-160lh">
                                                        O Sports Experience foi desenvolvido pela DirecTV como um hub centralizado para a Copa do Mundo 2022 no Qatar. O projeto foi criado em duas versões distintas - uma para o Brasil e outra para a América Latina - tendo como objetivo principal oferecer uma experiência completa e integrada aos usuários, permitindo o acesso a todas as informações, transmissões e estatísticas do torneio através de múltiplas plataformas: mobile, web e TV.
                                                    </p>

                                                    <p class="type-basic-160lh">
                                                        A plataforma foi projetada para oferecer uma experiência rica e completa, incluindo funcionalidades essenciais como transmissão de jogos ao vivo e sob demanda, calendário detalhado das partidas, estatísticas aprofundadas de times e jogadores, além de um sistema robusto de acompanhamento de resultados. Todas essas features foram cuidadosamente adaptadas para proporcionar a melhor experiência em cada dispositivo.
                                                    </p>

                                                    <p class="type-basic-160lh">
                                                        A integração com a FIFA foi um elemento crucial do projeto, permitindo acesso a dados oficiais em tempo real e garantindo a precisão das informações apresentadas. O desenvolvimento focou em manter uma consistência visual e funcional entre as diferentes plataformas, enquanto otimizava a experiência para as características únicas de cada meio. O projeto foi disponibilizado para toda a base de usuários da DirecTV durante o período da Copa do Mundo, incluindo novos assinantes que se cadastraram durante a copa.
                                                    </p>
                                                    <div>
                                                        <div class="regular-swiper swiper">
                                                            <div class="swiper-button-prev">
                                                                <i class="ph ph-arrow-left"></i>
                                                            </div>
                                                            <div class="swiper-button-next">
                                                                <i class="ph ph-arrow-right"></i>
                                                            </div>


                                                            <div class="swiper-wrapper">
                                                                <div
                                                                    class="swiper-slide swiper-slide--one mobile-stack">
                                                                    <img src="img/works/Sports/Overview/Overview1.png" alt="Slide 1">
                                                                </div>
                                                                <div
                                                                    class="swiper-slide swiper-slide--two mobile-stack">
                                                                    <img src="img/works/Sports/Overview/Overview2.png" alt="Slide 2">
                                                                </div>
                                                                <div
                                                                    class="swiper-slide swiper-slide--three mobile-stack">
                                                                    <img src="img/works/Sports/Overview/Overview3.png" alt="Slide 3">
                                                                </div>
                                                                <div
                                                                    class="swiper-slide swiper-slide--four mobile-stack">
                                                                    <img src="img/works/Sports/Overview/Overview4.png" alt="Slide 4">
                                                                </div>
                                                                <div
                                                                    class="swiper-slide swiper-slide--five mobile-stack">
                                                                    <img src="img/works/Sports/Overview/Overview5.png" alt="Slide 5">
                                                                </div>
                                                            </div>
                                                            <div class="swiper-pagination"></div>
                                                        </div>
                                                    </div>
                                                </div>


                                                <!-- Challenge Section -->
                                                <div id="desafio" class="project__block loading__item">
                                                    <h3>O Desafio</h3>
                                                    <p class="type-basic-160lh">
                                                        O principal desafio do projeto foi gerenciar um cronograma extremamente apertado enquanto coordenávamos alinhamentos com múltiplas empresas externas. A necessidade de desenvolver diferentes versões do produto para Brasil e América Latina, cada uma com suas próprias restrições e limitações de funcionalidades, adicionou uma camada extra de complexidade ao projeto.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        Para lidar com as diferentes restrições regionais, desenvolvemos layouts específicos para cada mercado. Esta abordagem personalizada nos permitiu enfatizar features distintas em cada região, compensando estrategicamente a ausência de certas funcionalidades com o destaque de outras, garantindo assim uma experiência rica para todos os usuários, independentemente de sua localização.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        A natureza dinâmica dos dados em tempo real apresentou desafios significativos, especialmente na versão mobile. O espaço limitado das telas menores exigiu soluções criativas para lidar com quebras inesperadas de layout causadas pela variação no volume e formato dos dados recebidos. Além disso, a gestão da comunicação de dados em tempo real demandou uma atenção especial para garantir a consistência das informações em todas as plataformas.
                                                    </p>

                                                    <p class="type-basic-160lh">
                                                        O alinhamento constante com a FIFA foi crucial para o sucesso do projeto. Reuniões frequentes permitiram a sincronização eficiente dos dados e a implementação ágil de mudanças necessárias, seja para adicionar novas funcionalidades ou ajustar features existentes de acordo com as especificidades de cada região. Esta comunicação próxima garantiu que o produto se mantivesse atualizado e alinhado com os padrões e requisitos oficiais da Copa do Mundo.
                                                    </p>

                                                    <div class="content__block grid-block">
                                                        <div class="container-fluid px-0">
                                                            <div class="my-gallery" itemscope itemtype="http://schema.org/ImageGallery">
                                                                <figure class="full-width-image-container" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/Sports/Desafio/Desafio.png" 
                                                                       data-size="4885x2443" 
                                                                       itemprop="contentUrl" 
                                                                       class="gallery__link">
                                                                        <img src="img/works/Sports/Desafio/Desafiot.png" 
                                                                             alt="Imagem de impacto do projeto"
                                                                             class="full-width-image" 
                                                                             itemprop="thumbnail">
                                                                    </a>
                                                                </figure>
                                                            </div>
                                                        </div>
                                                    </div>
                                                 </div>                                                  

                                                <!-- Role Section -->
                                                <div id="papel" class="project__block loading__item after-gallery">
                                                    <h3>Meu Papel</h3>
                                                    <p class="type-basic-160lh">
                                                        Como Designer de Produto Senior, atuei como ponte crucial entre as equipes de design e desenvolvimento. Minha função envolveu não apenas a criação e ajustes de layout em colaboração com a equipe de design da DirecTV, mas também a coordenação efetiva entre múltiplas equipes, incluindo o time de desenvolvimento e QA da Accenture e a equipe de dados da FIFA.
                                                    </p>

                                                    <p class="type-basic-160lh">
                                                        Um aspecto fundamental do meu papel foi a gestão de relacionamentos e expectativas. Participei de reuniões de alinhamento e apresentações regulares com stakeholders, equipes técnicas e de design, onde apresentava atualizações do projeto, coletava feedback e negociava ajustes necessários. Esta comunicação constante foi essencial para manter o projeto dentro do escopo e cronograma planejados.
                                                    </p>

                                                    <p class="type-basic-160lh">
                                                        Minhas responsabilidades incluíram garantir a entrega do sistema na data prevista, gerenciar a criação e ajustes de design, e coordenar as diversas equipes envolvidas. O gerenciamento ágil das necessidades do cliente, junto com os prazos propostos e funcionalidades requeridas, permitiu que adaptássemos rapidamente o projeto às mudanças emergenciais em layouts e requisitos.
                                                    </p>
                                                </div>

                                                <!-- Solution Section -->
                                                <div id="solucao" class="project__block loading__item">
                                                    <h3>A Solução</h3>
                                                    <p class="type-basic-160lh">
                                                        O design system e o produto em si foram estruturados com foco em criar uma experiência unificada e consistente entre todas as regiões e plataformas. A abordagem priorizou usabilidade e responsividade, considerando os diferentes contextos de uso - mobile, web e TV - e suas respectivas resoluções e particularidades de interação.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        Para lidar com os desafios dos dados em tempo real, estabelecemos um processo contínuo de alinhamento com os provedores de dados. Isso nos permitiu desenvolver e refinar soluções que garantiam a melhor apresentação das informações, mantendo a usabilidade do layout mesmo com atualizações constantes de conteúdo.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        Desenvolvemos um sistema robusto de componentes que incluía desde estatísticas individuais de jogadores até visualizações complexas como mapas de calor e posicionamento em campo. Cada componente foi projetado pensando em flexibilidade e adaptabilidade, permitindo sua utilização eficiente em diferentes contextos e plataformas.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        Para atender às diferentes necessidades regionais, implementamos uma estratégia de reorganização e priorização de componentes. Na versão brasileira, onde certas funcionalidades não estavam disponíveis devido a restrições legais, desenvolvemos soluções criativas que mantinham a qualidade da experiência do usuário através da ênfase em componentes alternativos e reorganização do conteúdo.
                                                    </p>
                                                     <!-- Nova seção de imagem responsiva -->
                                                
                                                <div class="infinite-scroll-section"></div>
                                                    <div class="tag-list">
                                                        <div class="fade"></div>

                                                        <div class="loop-slider"
                                                            style="--duration:15951ms; --direction:normal;">
                                                            <div class="inner">
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Action.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Action-1.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Action-2.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Action-3.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Action-4.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Action-5.png" alt="Projeto 2">
                                                                </div>
                                                               
                                                            </div>

                                                        </div>

                                                        <!-- Segunda linha - direção reversa -->
                                                        <div class="loop-slider"
                                                            style="--duration:15951ms; --direction:reverse;">
                                                            <div class="inner">
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Standings.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Standings-1.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Standings-2.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Standings-3.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Standings-4.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Standings-5.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/Standings-6.png" alt="Projeto 1">
                                                                </div>
                                                            </div>

                                                        </div>


                                                        <!-- Terceira linha - direção normal -->
                                                        <div class="loop-slider"
                                                            style="--duration:15951ms; --direction:normal;">
                                                            <div class="inner">
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/2x3-Unfocused.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/2x3-Unfocused Copy 2.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/2x3-Unfocused Copy 3.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/2x3-Unfocused Copy 4.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/2x3-Unfocused Copy 6.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/2x3-Unfocused.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/2x3-Unfocused Copy 2.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/2x3-Unfocused Copy 3.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/2x3-Unfocused Copy 4.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/2x3-Unfocused Copy 6.png" alt="Projeto 1">
                                                                </div>
                                                            </div>

                                                        </div>


                                                        <!-- Terceira linha - direção normal -->
                                                        <div class="loop-slider"
                                                            style="--duration:15951ms; --direction:reverse;">
                                                            <div class="inner">
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/cardLive.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/cardLive-1.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/cardLive-2.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/cardLive-3.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/cardLive-4.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/cardLive-5.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/cardLive-6.png" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/cardLive-7.png" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/Sports/Solucao/cardLive-8.png" alt="Projeto 1">
                                                                </div>
                                                            </div>

                                                        </div>

                                                    </div>
                                                </div>

                                                <!-- Delivery Section -->
                                                <div id="impacto" class="project__block loading__item">
                                                    <h3>A Entrega</h3>
                                                    <p class="type-basic-160lh">
                                                        A apresentação final do MVP foi recebida com grande entusiasmo pelos stakeholders. A demonstração detalhada das funcionalidades, incluindo o layout responsivo para todas as plataformas e a experiência fluida de navegação, evidenciou o potencial do projeto como um hub completo para a Copa do Mundo de 2022.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        O projeto entregue cumpriu todos os objetivos estabelecidos inicialmente, oferecendo uma experiência do usuário rápida e intuitiva para todos os aspectos da Copa do Mundo. O design responsivo garantiu uma visualização adequada em qualquer tamanho de tela, mantendo a consistência e qualidade da experiência em todas as plataformas.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        Um dos maiores aprendizados do projeto foi a experiência de trabalhar simultanêmente com múltiplas equipes - DirecTV, FIFA e Accenture - com um prazo final inflexível devido à data de início da Copa do Mundo. Este cenário proporcionou valiosas lições sobre priorização de tarefas e alinhamento de expectativas, resultando em um processo de desenvolvimento mais eficiente e coerente.
                                                    </p>
                                                     <div class="container-fluid px-0">
                                                            <div class="my-gallery" itemscope itemtype="http://schema.org/ImageGallery">
                                                                <figure class="full-width-image-container" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/Sports/Entrega/Entrega1.png" 
                                                                       data-size="4885x2443" 
                                                                       itemprop="contentUrl" 
                                                                       class="gallery__link">
                                                                        <img src="img/works/Sports/Entrega/Entrega1t.png" 
                                                                             alt="Imagem de impacto do projeto"
                                                                             class="full-width-image" 
                                                                             itemprop="thumbnail">
                                                                    </a>
                                                                </figure>
                                                            </div>
                                                            
                                                         </div>
                                                         <div class="container-fluid px-0">
                                                            <div class="my-gallery" itemscope itemtype="http://schema.org/ImageGallery">
                                                                <figure class="full-width-image-container" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/Sports/Entrega/Entrega2.png" 
                                                                       data-size="4885x2443" 
                                                                       itemprop="contentUrl" 
                                                                       class="gallery__link">
                                                                        <img src="img/works/Sports/Entrega/Entrega2t.png" 
                                                                             alt="Imagem de impacto do projeto"
                                                                             class="full-width-image" 
                                                                             itemprop="thumbnail">
                                                                    </a>
                                                                </figure>
                                                            </div>
                                                            
                                                         </div>

                                                <!-- Gallery Section -->
                                                <div id="galeria" class="project__block loading__item after-gallery">
                                                    <h3>Galeria</h3>
                                                    <p class="type-basic-160lh">
                                                        Esta seção apresenta materiais complementares do projeto, incluindo detalhamentos de componentes, telas adicionais e materiais de apresentação. São artefatos que, embora não façam parte do fluxo principal ou tenham sido descartadas devido a restrições de funcionalidade ou tempo, oferecem uma visão mais ampla do processo de design e da complexidade do projeto.
                                                    </p>
                                                    <div class="content__block grid-block">
                                                        <div class="container-fluid px-0 inner__gallery">
                                                            <!-- Portfolio Gallery Start -->
                                                            <div class="row gx-0 my-gallery" itemscope itemtype="http://schema.org/ImageGallery" data-masonry='{"percentPosition": true }'>
                                                                <!-- portfolio gallery single item -->
                                                                <figure class="col-12 col-md-6 gallery__item grid-item animate-card-2" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/Sports/Galeria/Colors.png" data-image="img/works/Sports/Galeria/Colorst.png" class="gallery__link" itemprop="contentUrl" data-size="3000x2500">
                                                                        <img src="img/works/Sports/Galeria/Colorst.png" class="gallery__image" itemprop="thumbnail" alt="Descrição da imagem">
                                                                    </a>
                                                                </figure>

                                                                <!-- portfolio gallery single item -->
                                                                <figure class="col-12 col-md-6 gallery__item grid-item animate-card-2" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/Sports/Galeria/Colors2.png" data-image="img/works/Sports/Galeria/Colors2t.png" class="gallery__link" itemprop="contentUrl" data-size="1100x1400">
                                                                        <img src="img/works/Sports/Galeria/Colors2t.png" class="gallery__image" itemprop="thumbnail" alt="Image description">
                                                                    </a>
                                                                </figure>

                                                                <!-- portfolio gallery single item -->
                                                                <figure class="col-12 gallery__item grid-item animate-card-2" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/Sports/Galeria/Colors3.png" data-image="img/works/Sports/Galeria/Colors3t.png" class="gallery__link" itemprop="contentUrl" data-size="4000x2000">
                                                                        <img src="img/works/Sports/Galeria/Colors3t.png" class="gallery__image" itemprop="thumbnail" alt="Descrição da imagem em largura total">
                                                                    </a>
                                                                </figure>

                                                                <!-- portfolio gallery single item -->
                                                                <figure class="col-12 col-md-6 gallery__item grid-item animate-card-2" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/Sports/Galeria/profile.png" data-image="img/works/Sports/Galeria/profilet.png" class="gallery__link" itemprop="contentUrl" data-size="1316x1906">
                                                                        <img src="img/works/Sports/Galeria/profilet.png" class="gallery__image" itemprop="thumbnail" alt="Descrição da imagem em largura total">
                                                                    </a>
                                                                </figure>

                                                                <!-- portfolio gallery single item -->
                                                                <figure class="col-12 col-md-6 gallery__item grid-item animate-card-2" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/Sports/Galeria/bet.png" data-image="img/works/Sports/Galeria/bett.png" class="gallery__link" itemprop="contentUrl" data-size="1316x1906">
                                                                        <img src="img/works/Sports/Galeria/bett.png" class="gallery__image" itemprop="thumbnail" alt="Descrição da imagem em largura total">
                                                                    </a>
                                                                </figure>

                                                                <!-- portfolio gallery single item -->
                                                                <figure class="col-12 col-md-6 gallery__item grid-item animate-card-2" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/Sports/Galeria/static2.png" data-image="img/works/Sports/Galeria/static2t.png" class="gallery__link" itemprop="contentUrl" data-size="1316x1906">
                                                                        <img src="img/works/Sports/Galeria/static2t.png" class="gallery__image" itemprop="thumbnail" alt="Descrição da imagem em largura total">
                                                                    </a>
                                                                </figure>
                                                                <!-- portfolio gallery single item -->
                                                                <figure class="col-12 col-md-6 gallery__item grid-item animate-card-2" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/Sports/Galeria/heat.png" data-image="img/works/Sports/Galeria/heatt.png" class="gallery__link" itemprop="contentUrl" data-size="1316x1906">
                                                                        <img src="img/works/Sports/Galeria/heatt.png" class="gallery__image" itemprop="thumbnail" alt="Descrição da imagem em largura total">
                                                                    </a>
                                                                </figure>
                                                            </div>
                                                            <!-- Portfolio Gallery End -->
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Footer Section -->
                                                <div class="project__block loading__item footer-section">
                                                    <div class="footer-content">
                                                        <div class="footer-grid">
                                                            <!-- Text Content -->
                                                            <div class="footer-text">
                                                                <h2>Vamos Conversar!</h2>
                                                                <p class="type-basic-160lh">
                                                                    Interessado em me ter na sua equipe? Deixe-me uma mensagem e retornarei o mais rápido possível.
                                                                </p>
                                                            </div>
                                                            <!-- Social Icons -->
                                                            <div class="social-icons">
                                                                <a href="mailto:<EMAIL>?subject=Mensagem%20do%20Portfolio" class="social-icon">
                                                                    <i class="ph ph-envelope"></i>
                                                                </a>
                                                                <a href="https://www.linkedin.com/in/lucas-scho/" class="social-icon">
                                                                    <i class="ph ph-linkedin-logo"></i>
                                                                </a>
                                                                <a href="https://www.instagram.com/lucaslsm" class="social-icon">
                                                                    <i class="ph ph-instagram-logo"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- New Two-Column Layout End -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Mobile Menu Bottom Placeholder Start -->
    <div class="header-offset"></div>
    <!-- Mobile Menu Bottom Placeholder End -->

    <!-- To Top Button Start -->
    <a href="#0" id="to-top" class="btn btn-to-top slide-up">
        <i class="ph ph-arrow-up"></i>
    </a>
    <!-- To Top Button End -->

    </div>
        <!-- Root element of PhotoSwipe. Must have class pswp. -->
        <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">

            <!-- Background of PhotoSwipe.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        It's a separate element, as animating opacity is faster than rgba(). -->
            <div class="pswp__bg"></div>
    
            <!-- Slides wrapper with overflow:hidden. -->
            <div class="pswp__scroll-wrap">
    
                <!-- Container that holds slides. PhotoSwipe keeps only 3 slides in DOM to save memory. -->
                <!-- don't modify these 3 pswp__item elements, data is added later on. -->
                <div class="pswp__container">
                    <div class="pswp__item"></div>
                    <div class="pswp__item"></div>
                    <div class="pswp__item"></div>
                </div>
    
                <!-- Default (PhotoSwipeUI_Default) interface on top of sliding area. Can be changed. -->
                <div class="pswp__ui pswp__ui--hidden">
    
                    <div class="pswp__top-bar">
    
                        <!--  Controls are self-explanatory. Order can be changed. -->
    
                        <div class="pswp__counter"></div>
    
                        <button class="pswp__button pswp__button--close link-s" title="Close (Esc)"></button>
    
                        <button class="pswp__button pswp__button--share link-s" title="Share"></button>
    
                        <button class="pswp__button pswp__button--fs link-s" title="Toggle fullscreen"></button>
    
                        <button class="pswp__button pswp__button--zoom link-s" title="Zoom in/out"></button>
    
                        <!-- Preloader demo http://codepen.io/dimsemenov/pen/yyBWoR -->
                        <!-- element will get class pswp__preloader-active when preloader is running -->
                        <div class="pswp__preloader">
                            <div class="pswp__preloader__icn">
                                <div class="pswp__preloader__cut">
                                    <div class="pswp__preloader__donut"></div>
                                </div>
                            </div>
                        </div>
                    </div>
    
                    <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
                        <div class="pswp__share-tooltip"></div>
                    </div>
    
                    <button class="pswp__button pswp__button--arrow--left link-s" title="Previous (arrow left)"></button>
    
                    <button class="pswp__button pswp__button--arrow--right link-s" title="Next (arrow right)"></button>
    
                    <div class="pswp__caption">
                        <div class="pswp__caption__center"></div>
                    </div>
    
                </div>
    
            </div>
    
        </div>

    <!-- Load Scripts Start -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="js/libs.min.js"></script>
    <script src="js/gallery-init.js"></script>
    <script src="js/app.js"></script>
    <script src="js/carossel.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <script>
        document.querySelectorAll('.lightboxgallery-link').forEach(item => {
            item.addEventListener('click', event => {
                event.preventDefault();
                const imgSrc = event.currentTarget.href;
                const overlay = document.createElement('div');
                overlay.classList.add('lightboxgallery-overlay');
                overlay.innerHTML = `<img src="${imgSrc}">`;
                document.body.appendChild(overlay);

                overlay.addEventListener('click', () => {
                    document.body.removeChild(overlay);
                });
            });
        });
    </script>

    <!-- Load Scripts End -->
</body>

</html>
